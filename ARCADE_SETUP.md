# 街机页面搭建完成 - 安装说明

## 📁 已创建的文件列表

### 1. 核心页面文件
- `themes/anzhiyu/layout/page.pug` - 已添加街机页面路由
- `themes/anzhiyu/layout/includes/page/arcade.pug` - 街机页面模板
- `source/arcade/index.md` - 街机页面主页
- `source/_data/arcade.yml` - 街机游戏数据配置

### 2. 游戏页面文件 (12个游戏)
- `source/arcade/kof97/index.md` - 拳皇97
- `source/arcade/sf2/index.md` - 街头霸王2
- `source/arcade/metalslug/index.md` - 合金弹头
- `source/arcade/contra/index.md` - 魂斗罗
- `source/arcade/samurai/index.md` - 侍魂
- `source/arcade/garou/index.md` - 饿狼传说
- `source/arcade/1945/index.md` - 彩京1945
- `source/arcade/sor/index.md` - 怒之铁拳
- `source/arcade/bubblebobble/index.md` - 泡泡龙
- `source/arcade/tetris/index.md` - 俄罗斯方块
- `source/arcade/sokoban/index.md` - 推箱子
- `source/arcade/pacman/index.md` - 吃豆人

### 3. 资源说明文件
- `source/images/arcade/README.md` - 图片资源说明
- `source/roms/README.md` - ROM文件说明

## 🚀 部署步骤

### 1. 准备图片资源
```bash
# 创建图片目录
mkdir -p source/images/arcade

# 添加以下图片文件：
# - top_arcade.jpg (顶部背景图)
# - kof97.jpg, sf2.jpg, metalslug.jpg 等游戏封面图
```

### 2. 准备ROM文件 (可选)
```bash
# 创建ROM目录
mkdir -p source/roms

# 添加合法的ROM文件：
# - kof97.zip, sf2.zip, metalslug.zip 等
# 注意：请确保ROM文件的合法性
```

### 3. 生成静态文件
```bash
# 清理并重新生成
hexo clean
hexo generate

# 本地预览
hexo server
```

### 4. 访问页面
- 街机主页：`http://localhost:4000/arcade/`
- 游戏页面：`http://localhost:4000/arcade/kof97/` 等

## 🎮 功能特性

### 街机主页
- ✅ 完全复制游戏页面结构
- ✅ 三个分类：格斗游戏、动作射击、益智休闲
- ✅ 12个经典街机游戏
- ✅ 响应式设计，支持移动端

### 游戏页面
- ✅ 集成EmulatorJS模拟器
- ✅ 支持街机ROM文件
- ✅ 游戏介绍和操作说明
- ✅ 统一的页面设计风格

### EmulatorJS集成
- ✅ 使用官方CDN加载
- ✅ 支持fbneo核心 (街机模拟)
- ✅ 自动开始游戏
- ✅ 保存游戏进度
- ✅ 全屏模式支持

## 🎨 自定义配置

### 修改游戏列表
编辑 `source/_data/arcade.yml` 文件：
```yaml
- name: 新游戏名称
  platform: 街机
  rating: ⭐⭐⭐⭐⭐
  description: 游戏描述
  image: /images/arcade/newgame.jpg
  link: /arcade/newgame/
```

### 添加新游戏页面
1. 创建游戏目录：`source/arcade/newgame/`
2. 添加 `index.md` 文件
3. 配置EmulatorJS参数
4. 添加游戏ROM文件

### 修改页面样式
街机页面使用与游戏页面相同的CSS样式，如需自定义：
1. 检查现有的游戏页面样式
2. 在主题CSS中添加 `.ArcadePage` 相关样式

## 🔧 故障排除

### 常见问题

1. **页面404错误**
   - 确保已重新生成静态文件：`hexo clean && hexo generate`
   - 检查文件路径是否正确

2. **游戏无法加载**
   - 检查ROM文件是否存在且路径正确
   - 确认ROM文件格式兼容EmulatorJS
   - 检查浏览器控制台错误信息

3. **图片不显示**
   - 确认图片文件存在于 `source/images/arcade/` 目录
   - 检查图片文件名与配置是否一致
   - 确保图片格式为JPG或PNG

4. **样式异常**
   - 清理浏览器缓存
   - 检查CSS文件是否正确加载

### 性能优化

1. **图片优化**
   - 压缩图片文件大小
   - 使用WebP格式 (如果支持)
   - 启用图片懒加载

2. **ROM文件优化**
   - 使用压缩的ROM文件
   - 考虑使用CDN托管大文件

## 📝 注意事项

1. **版权合规**：确保所有ROM文件的使用符合版权法规
2. **性能考虑**：ROM文件较大，建议使用CDN或优化加载
3. **浏览器兼容**：EmulatorJS需要现代浏览器支持
4. **移动端体验**：在移动设备上测试游戏操作体验

## 🎯 下一步建议

1. 添加更多经典街机游戏
2. 优化移动端操作体验
3. 添加游戏排行榜功能
4. 集成用户评论系统
5. 添加游戏攻略和技巧

---

**街机页面搭建完成！** 🎉

访问 `/arcade/` 开始体验经典街机游戏吧！
