# ROM文件说明

## 目录结构

```
source/roms/
├── pokemon-emerald.gba      # 宝可梦绿宝石 ROM文件
├── pokemon-leafgreen.gba    # 宝可梦叶绿 ROM文件
├── zelda-alttp.gba         # 塞尔达传说：众神的三角力量 ROM文件
├── dragonball-adventure.gba # 龙珠大冒险 ROM文件
└── knights-of-valour.zip   # 三国战纪 ROM文件（街机）
```

## ⚠️ 重要声明

### 版权说明
1. **本项目不提供任何ROM文件**
2. **用户需要自行获取合法的ROM文件**
3. **仅支持用户拥有原版游戏的ROM备份**
4. **严禁用于商业用途**

### 合法获取ROM的方式
1. **自有游戏备份**：从自己拥有的原版游戏卡带中提取
2. **官方数字版**：购买官方重制版或数字版
3. **开源替代**：使用开源的同类游戏

## 技术要求

### GBA游戏 (.gba文件)
- **模拟器核心**：mGBA
- **文件格式**：.gba
- **文件大小**：通常8-32MB
- **兼容性**：确保与EmulatorJS的mGBA核心兼容

### 街机游戏 (.zip文件)
- **模拟器核心**：FBNeo
- **文件格式**：.zip（包含ROM和配置文件）
- **文件大小**：通常10-100MB
- **兼容性**：确保与EmulatorJS的FBNeo核心兼容

## 文件配置

### 文件命名规范
确保ROM文件名与页面配置中的`EJS_gameUrl`参数一致：

```javascript
// 宝可梦绿宝石
EJS_gameUrl = '/roms/pokemon-emerald.gba';

// 宝可梦叶绿
EJS_gameUrl = '/roms/pokemon-leafgreen.gba';

// 塞尔达传说
EJS_gameUrl = '/roms/zelda-alttp.gba';

// 龙珠大冒险
EJS_gameUrl = '/roms/dragonball-adventure.gba';

// 三国战纪
EJS_gameUrl = '/roms/knights-of-valour.zip';
```

## 替代方案

如果无法获取ROM文件，可以考虑：

### 1. 演示模式
- 移除EmulatorJS代码
- 仅展示游戏介绍和截图
- 提供外部链接到合法游戏平台

### 2. 开源游戏
- 使用开源的同类游戏
- 自制的homebrew游戏
- 免费发布的独立游戏

### 3. 外部链接
- 链接到官方游戏平台
- 链接到合法的在线模拟器
- 链接到游戏购买页面

## 性能优化

### 文件压缩
- GBA文件可以使用gzip压缩
- 街机ROM通常已经是压缩格式
- 考虑使用CDN托管大文件

### 加载优化
- 实现文件预加载
- 显示加载进度条
- 提供离线缓存选项

## 故障排除

### 常见问题
1. **游戏无法加载**
   - 检查ROM文件路径是否正确
   - 确认文件格式是否支持
   - 检查文件是否损坏

2. **游戏运行异常**
   - 尝试不同的模拟器核心
   - 检查ROM文件完整性
   - 查看浏览器控制台错误

3. **性能问题**
   - 降低游戏分辨率
   - 关闭不必要的特效
   - 使用更快的浏览器

## 免责声明

本项目仅提供技术框架和教程，不承担任何版权相关责任。用户应自行确保所使用ROM文件的合法性，并遵守相关法律法规。

---

**请务必遵守版权法律，支持正版游戏！**
