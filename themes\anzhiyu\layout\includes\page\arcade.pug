#arcade
  if site.data.arcade
    each i in site.data.arcade
      .author-content.author-content-item.ArcadePage.single(style = `background: url(${i.top_background}) left 37% / cover no-repeat !important;`)
        .card-content
          .author-content-item-tips=i.class_name
          span.author-content-item-title=i.description
          .content-bottom
            .tips=i.tip
          .banner-button-group
            a.banner-button(href=i.buttonLink)
              i.anzhiyufont.anzhiyu-icon-arrow-circle-right(style='font-size: 1.3rem')
              span.banner-button-text=i.buttonText
      each item in i.good_arcade
        .goodarcade-item
          h2.goodarcade-title= item.title
          .goodarcade-item-description= item.description
          .arcade-item
            .arcade-item-content
              each iten, indey in item.arcade_list
                .arcade-item-content-item
                  .arcade-item-content-item-cover
                    if iten.back_image
                      .flip-card-inner
                        .flip-card-front
                          img.arcade-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                        .flip-card-back
                          img.arcade-item-back-image(data-lazy-src=url_for(iten.back_image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                    else
                      img.arcade-item-content-item-image(data-lazy-src=url_for(iten.image) onerror=`this.onerror=null;this.src='` + url_for(theme.error_img.flink) + `'` alt=iten.name)
                  .arcade-item-content-item-info
                    .arcade-item-content-item-name(onclick=`rm.rightmenuCopyText("${iten.name}");anzhiyu.snackbarShow("${_p('已复制街机游戏名：') + " 【" + iten.name + "】"}");` title=iten.name)= iten.name
                    .arcade-item-content-item-specification
                      if iten.platform
                        span.arcade-item-content-item-specification-platform= iten.platform + " / "
                      if iten.rating
                        span.arcade-item-content-item-specification-rating= iten.rating
                      if iten.play_date
                        span.arcade-item-content-item-specification-play-date= " / " + iten.play_date
                      if iten.achievements
                        span.arcade-item-content-item-specification-achievements= " / " + iten.achievements
                    .arcade-item-content-item-description= iten.description
                    .arcade-item-content-item-toolbar
                      if iten.link.includes('https://') || iten.link.includes('http://')
                        a.arcade-item-content-item-link(href= iten.link, target='_blank') 详情
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
                      else  
                        a.arcade-item-content-item-link(href= iten.link, target='_blank') 查看文章
                        .bber-reply(onclick="rm.rightMenuCommentText(" + `'${iten.name + " " + iten.specification + " " + iten.description}'` + ")")
                          i.anzhiyufont.anzhiyu-icon-message
