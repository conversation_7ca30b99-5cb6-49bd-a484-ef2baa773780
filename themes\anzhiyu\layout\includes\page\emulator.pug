#emulator-page
  .game-selector(id="game-selector")
    h2 选择游戏
    .game-grid
      each gameData, gameId in site.data.emulator_games.games
        .game-card(onclick=`loadGame('${gameId}')`)
          .game-info
            h3= gameData.name
            p.platform= gameData.platform
            p.description= gameData.description.substring(0, 100) + '...'

  .emulator-container(id="emulator-container" style="display: none;")
    .game-header(id="game-header")
    .emulator-wrapper
      #game-container
        #game(style="width: 100%; height: 600px;")
    .game-info-section(id="game-info")
    .back-button-container
      button.back-button(onclick="showGameSelector()") ← 返回游戏选择

  // 游戏数据传递给JavaScript
  script.
    window.gamesData = !{JSON.stringify(site.data.emulator_games.games)};

    function loadGame(gameId) {
      const gameData = window.gamesData[gameId];
      if (!gameData) return;

      // 隐藏游戏选择器
      document.getElementById('game-selector').style.display = 'none';
      document.getElementById('emulator-container').style.display = 'block';

      // 设置游戏标题
      const gameHeader = document.getElementById('game-header');
      gameHeader.innerHTML = `
        <h1 class="game-title">${gameData.name}</h1>
        ${gameData.english_name ? `<p class="game-english-name">${gameData.english_name}</p>` : ''}
        <div class="game-meta">
          <span class="game-platform">${gameData.platform}</span>
          <span class="game-color" style="color: ${gameData.color || '#425AEF'}">●</span>
        </div>
      `;

      // 设置游戏容器样式
      const gameContainer = document.getElementById('game-container');
      gameContainer.style.border = `2px solid ${gameData.color || '#425AEF'}`;
      gameContainer.style.borderRadius = '10px';
      gameContainer.style.overflow = 'hidden';
      gameContainer.style.margin = '20px 0';

      // 设置游戏信息
      const gameInfo = document.getElementById('game-info');
      let infoHTML = `
        <div class="game-description">
          <p>${gameData.description}</p>
        </div>
      `;

      if (gameData.features) {
        infoHTML += `
          <div class="game-features">
            <h3>游戏特色</h3>
            <ul>
              ${gameData.features.map(feature => `<li>${feature}</li>`).join('')}
            </ul>
          </div>
        `;
      }

      if (gameData.controls) {
        infoHTML += `
          <div class="game-controls">
            <h3>操作说明</h3>
            <ul>
              ${gameData.controls.map(control => `<li>${control}</li>`).join('')}
            </ul>
          </div>
        `;
      }

      if (gameData.tips) {
        infoHTML += `<div class="game-tips"><h3>游戏攻略提示</h3>`;
        gameData.tips.forEach(tip => {
          infoHTML += `
            <div class="tip-section">
              <h4>${tip.title}</h4>
              <ul>
                ${tip.content.map(item => `<li>${item}</li>`).join('')}
              </ul>
            </div>
          `;
        });
        infoHTML += `</div>`;
      }

      infoHTML += `
        <div class="save-notice">
          <h3>保存说明</h3>
          <ul>
            <li>游戏支持自动保存功能</li>
            <li>可以使用游戏内的存档系统</li>
            <li>建议定期保存游戏进度</li>
          </ul>
        </div>
        <div class="copyright-notice">
          <p><em>注意：请确保ROM文件的合法性，仅用于个人学习和怀旧目的。</em></p>
        </div>
      `;

      gameInfo.innerHTML = infoHTML;

      // 配置并加载EmulatorJS
      window.EJS_player = '#game';
      window.EJS_core = gameData.core;
      window.EJS_gameUrl = `/roms/${gameData.rom_file}`;
      window.EJS_pathtodata = 'https://cdn.emulatorjs.org/stable/data/';
      window.EJS_startOnLoaded = true;
      window.EJS_gameName = gameData.english_name || gameData.name;
      window.EJS_color = gameData.color || '#425AEF';
      window.EJS_VirtualGamepadSettings = {
        'showVirtualGamepad': true,
        'virtualGamepadAlpha': 0.7
      };

      // 动态加载EmulatorJS
      if (!document.querySelector('script[src*="emulatorjs"]')) {
        const script = document.createElement('script');
        script.src = 'https://cdn.emulatorjs.org/stable/data/loader.js';
        document.head.appendChild(script);
      }
    }

    function showGameSelector() {
      document.getElementById('game-selector').style.display = 'block';
      document.getElementById('emulator-container').style.display = 'none';

      // 清理游戏容器
      document.getElementById('game').innerHTML = '';
    }

    // 检查URL参数自动加载游戏
    window.addEventListener('load', function() {
      const urlParams = new URLSearchParams(window.location.search);
      const gameParam = urlParams.get('game');
      if (gameParam && window.gamesData[gameParam]) {
        loadGame(gameParam);
      }
    });

  style.
    .game-selector {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .game-selector h2 {
      text-align: center;
      color: var(--anzhiyu-fontcolor);
      margin-bottom: 30px;
      font-size: 2rem;
    }

    .game-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .game-card {
      background: var(--anzhiyu-card-bg);
      border-radius: 15px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .game-card:hover {
      transform: translateY(-5px);
      border-color: var(--anzhiyu-main);
      box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .game-card h3 {
      color: var(--anzhiyu-fontcolor);
      margin-bottom: 10px;
      font-size: 1.3rem;
    }

    .game-card .platform {
      background: var(--anzhiyu-main);
      color: white;
      padding: 3px 10px;
      border-radius: 15px;
      font-size: 0.8rem;
      display: inline-block;
      margin-bottom: 10px;
    }

    .game-card .description {
      color: var(--anzhiyu-secondtext);
      line-height: 1.5;
      font-size: 0.9rem;
    }

    .emulator-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .game-header {
      text-align: center;
      margin-bottom: 30px;
    }

    .game-title {
      font-size: 2.5rem;
      margin-bottom: 10px;
      color: var(--anzhiyu-fontcolor);
    }

    .game-english-name {
      font-size: 1.2rem;
      color: var(--anzhiyu-secondtext);
      margin-bottom: 15px;
    }

    .game-meta {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 15px;
      font-size: 1.1rem;
    }

    .game-platform {
      background: var(--anzhiyu-main);
      color: white;
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 0.9rem;
    }

    .game-description {
      background: var(--anzhiyu-card-bg);
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .game-features, .game-controls, .game-tips, .save-notice {
      background: var(--anzhiyu-card-bg);
      padding: 20px;
      border-radius: 10px;
      margin-bottom: 20px;
    }

    .game-features h3, .game-controls h3, .game-tips h3, .save-notice h3 {
      color: var(--anzhiyu-main);
      margin-bottom: 15px;
      border-bottom: 2px solid var(--anzhiyu-main);
      padding-bottom: 5px;
    }

    .game-features ul, .game-controls ul, .save-notice ul {
      list-style: none;
      padding: 0;
    }

    .game-features li, .game-controls li, .save-notice li {
      padding: 8px 0;
      border-bottom: 1px solid var(--anzhiyu-card-border);
      position: relative;
      padding-left: 20px;
    }

    .game-features li:before, .game-controls li:before, .save-notice li:before {
      content: "▶";
      color: var(--anzhiyu-main);
      position: absolute;
      left: 0;
    }

    .tip-section {
      margin-bottom: 20px;
    }

    .tip-section h4 {
      color: var(--anzhiyu-secondtext);
      margin-bottom: 10px;
      font-size: 1.1rem;
    }

    .tip-section ul {
      list-style: none;
      padding: 0;
      margin-left: 15px;
    }

    .tip-section li {
      padding: 5px 0;
      position: relative;
      padding-left: 15px;
    }

    .tip-section li:before {
      content: "•";
      color: var(--anzhiyu-main);
      position: absolute;
      left: 0;
    }

    .emulator-wrapper {
      margin: 30px 0;
    }

    #game-container {
      box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .copyright-notice {
      text-align: center;
      margin-top: 30px;
      padding: 15px;
      background: var(--anzhiyu-secondbg);
      border-radius: 10px;
      color: var(--anzhiyu-secondtext);
    }

    .back-button-container {
      text-align: center;
      margin: 20px 0;
    }

    .back-button {
      background: var(--anzhiyu-main);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 25px;
      cursor: pointer;
      font-size: 1rem;
      transition: all 0.3s ease;
    }

    .back-button:hover {
      background: var(--anzhiyu-main-op);
      transform: translateY(-2px);
    }

    @media (max-width: 768px) {
      .game-grid {
        grid-template-columns: 1fr;
      }

      .emulator-container {
        padding: 10px;
      }

      .game-title {
        font-size: 2rem;
      }

      #game {
        height: 400px !important;
      }
    }
